# CLAUDE.md - NovelWebsite 開發原則

本文件定義了 NovelWebsite 專案的核心開發原則，所有開發者必須遵循。

## 🚨 核心開發原則

### 1. 異步優先 (Async-First)
- 後端使用異步框架 (Scrapy, Django 4.2+)
- **嚴禁**在 Scrapy 相關模組中使用阻塞 I/O (`requests`, 同步 `os` 調用, `asyncio.run()`)
- 所有網路 I/O 必須使用 `scrapy.Request` 或 `aiohttp`
- **關鍵**: 絕不在 Scrapy spiders 中使用 `asyncio.run()` - 會造成事件循環衝突

### 2. 遵循介面 (Interface Compliance)
- 所有爬蟲適配器必須繼承 `BaseAdapter` 介面 (`backend/novel/adapters/base_adapter.py`)
- 實作前必須閱讀並理解基礎介面定義

### 3. 配置外部化 (Externalized Configuration)
- 嚴禁在前端硬編碼業務邏輯選項（如分類、狀態）
- 所有業務配置必須透過後端 API 提供

### 4. CI 合規 (CI Compliance)
- 所有程式碼必須通過 `make ci-check`
- 不要添加不必要的 linter 忽略（特別是 `F401`）
- 使用 `logging` 模組進行調試，而非 `print()`
- CI 執行時間目標：< 2 秒（Tier 2 架構）
- **commit push完要確認CI被正確觸發並運行**

### 5. 測試覆蓋 (Test Coverage)
- 新業務邏輯必須配備對應的單元測試
- 適配器解析方法必須使用本地 fixtures 進行測試
- PR 合併前所有 CI 檢查必須通過

### 6. 依賴管理 (Dependency Management)
- 遵循「零依賴原則」：避免為少量使用引入大型依賴
- 圖示使用內聯 SVG，而非臃腫的圖示庫
- 定期審查並清理未使用的依賴
- 專案使用pnpm而非npm

### 7. 安全最佳實踐 (Security Best Practices)
- 永不提交敏感資訊（密鑰、密碼、令牌）
- 使用環境變數或 Doppler 管理敏感配置
- 可重用 Actions 必須包含安全警告和使用限制
- **安全漏洞修復流程**：
  - 使用 `pnpm audit` 定期檢查依賴漏洞
  - 高危漏洞必須立即修復（P0 優先級）
  - 使用 `pnpm install --fix-lockfile` 重新生成依賴樹
  - CI 流程包含自動安全檢查，阻塊高危漏洞

### 8. 程式碼品質 (Code Quality)
- 遵循專案既有的程式碼風格
- 使用現有的工具和函式庫，而非重新發明輪子
- 保持程式碼簡潔可讀，必要時添加註解

### 9. 效能考量 (Performance Considerations)
- 優先考慮非阻塞操作
- 使用批量操作而非單一操作循環
- 利用快取減少重複計算和請求

### 10. 文件同步 (Documentation Sync)
- 重大變更後更新相關文件
- 保持 API 文件與實際實作同步
- 複雜邏輯必須有清晰的註解說明

---

## 🎯 MVP 戰略原則

### 「黃金28」完本小說戰略
- **核心理念**: 先完美內容體驗 → 再擴大內容規模
- **目標**: 28本市場驗證的頂級完本小說
- **主要來源**: 黃金屋中文 (hjwzw.com)
- **價值主張**: 為每本書提供完美的閱讀體驗

### 開發優先級
1. **P0 立即執行**: 黃金屋搜索機制研究 + 內容爬取
2. **P1 核心功能**: 用戶系統 (註冊、登入、書架)
3. **P2 擴展優化**: SEO 優化 + 響應式設計完善

### MVP 成功標準
- 28本書內容100%完整可讀
- 用戶註冊→收藏→書架的完整循環
- 首批用戶能順暢閱讀任一完本小說

---

## 📚 關鍵文檔參考

### 專案架構
- `docs/project-structure-annotated.md` - 完整項目架構說明 (143 目錄, 563 檔案)
- `docs/project-structure-tree.md` - 目錄樹結構
- `docs/development-timeline.md` - 開發歷程記錄 (39 個 PR)

### CI/CD 配置
- `.github/workflows/main-ci.yml` - 主要 CI 流程 (Tier 2 架構)
- `.github/actions/run-in-docker/` - 可重用 Docker Action
- `docs/cicd-strategy.md` - CI/CD 策略與安全考量

### 開發指南
- `README.md` - 專案概述與快速開始
- `Makefile` - 統一開發指令介面
- `docs/makefile-guide.md` - Makefile 使用指南

### 業務邏輯
- `backend/novel/adapters/base_adapter.py` - 爬蟲基礎介面
- `docs/MVP_NOVEL_LIST.md` - MVP 小說清單
- `docs/novel-weblist.md` - 目標網站列表

---

## 🔬 失敗經驗總結

### 失敗經驗方法論
- 總結每次失敗的根本原因，提取可復用的學習經驗
- 針對失敗進行系統性反思，而非個人歸咎
- 建立失敗的結構化分析框架，提高未來決策質量

### 方法論失敗案例

#### 📊 這次失敗的核心方法論問題

  1. "症狀驅動" vs "根因驅動"

  - 錯誤: 看到 react-scripts: not found 就立即想到映像、工作流、架構問題
  - 正確: 應該先檢查最基礎的 package.json 配置是否正確
  - 教訓: 永遠從最簡單、最基礎的配置開始排查

  2. "過早架構化" 陷阱

  - 錯誤: 在確認基礎問題前就開始設計複雜的映像分離、預烘焙策略
  - 正確: 先讓基本功能正常運行，再考慮架構優化
  - 教訓: "Make it work, then make it better"

  3. "假設堆疊" 問題

  - 錯誤: 基於錯誤假設（Alpine 問題、沙箱問題）進行多層修復
  - 正確: 每個假設都要驗證，不要在錯誤假設上堆疊解決方案
  - 教訓: 驗證每一層假設的正確性

  🔧 後續處理策略：務實優先原則

**最後更新**: 2025-06-22

---

## §5. 任務層級與治理公理 (Task Hierarchy & Governance Axiom)

### 5.1 任務原子性與分解原則
- **原則**: 所有由 `Navigator` 創建的頂級任務（Epics），如果其複雜度較高，**必須**由 `Task-Dispatcher` Agent 將其分解為更小、可獨立執行的子任務（Sub-Issues）。
- **目標**: 確保每個開發單元（PR）都聚焦於一個清晰、有限的目標。

### 5.2 原生層級鏈接公理 (Native Hierarchy Linking Axiom)
- **規則**: `Task-Dispatcher` 在創建子任務時，**必須**通過 GitHub API（GraphQL）建立**原生父子層級關係**。
- **禁止**: 禁止使用非結構化的文本描述（如在正文中寫 "sub-task of..."）作為唯一的鏈接方式。這確保了任務關係在整個 GitHub 平台（Issues, Projects）中的可追蹤性和一致性。

### 5.3 上下文繼承原則 (Context Inheritance Principle)
- **規則**: 所有子任務**必須**在創建時，由創建者（`Task-Dispatcher`）自動繼承其父任務的關鍵上下文標籤，包括但不限於：
  - **優先級標籤**: `P0:阻塞`, `P1:核心`, `S級:戰略` 等。
  - **戰略分類標籤**: `MVP:黃金28內容`, `MVP:5分鐘旅程` 等。
- **目標**: 確保即使任務被分解，其原始的戰略意圖和緊急性也能無損地傳遞到每一個執行單元。